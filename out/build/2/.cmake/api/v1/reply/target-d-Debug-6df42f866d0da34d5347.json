{"artifacts": [{"path": "d"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 4, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g"}], "language": "C", "sourceIndexes": [0]}], "id": "d::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"fragment": "", "role": "flags"}], "language": "C"}, "name": "d", "nameOnDisk": "d", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "main.c", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}