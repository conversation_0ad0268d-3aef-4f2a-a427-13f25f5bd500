{"configurations": [{"directories": [{"build": ".", "minimumCMakeVersion": {"string": "3.10.0"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "d", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "d::@6890427a1f51a3e7e1df", "jsonFile": "target-d-Debug-6df42f866d0da34d5347.json", "name": "d", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/ddd/out/build/2", "source": "/home/<USER>/ddd"}, "version": {"major": 2, "minor": 1}}