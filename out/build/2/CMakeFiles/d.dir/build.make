# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.18

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Disable VCS-based implicit rules.
% : %,v


# Disable VCS-based implicit rules.
% : RCS/%


# Disable VCS-based implicit rules.
% : RCS/%,v


# Disable VCS-based implicit rules.
% : SCCS/s.%


# Disable VCS-based implicit rules.
% : s.%


.SUFFIXES: .hpux_make_needs_suffix_list


# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/ddd

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/ddd/out/build/2

# Include any dependencies generated for this target.
include CMakeFiles/d.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/d.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/d.dir/flags.make

CMakeFiles/d.dir/main.c.o: CMakeFiles/d.dir/flags.make
CMakeFiles/d.dir/main.c.o: ../../../main.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ddd/out/build/2/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/d.dir/main.c.o"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/d.dir/main.c.o -c /home/<USER>/ddd/main.c

CMakeFiles/d.dir/main.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/d.dir/main.c.i"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/ddd/main.c > CMakeFiles/d.dir/main.c.i

CMakeFiles/d.dir/main.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/d.dir/main.c.s"
	/usr/bin/arm-linux-gnueabihf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/ddd/main.c -o CMakeFiles/d.dir/main.c.s

# Object files for target d
d_OBJECTS = \
"CMakeFiles/d.dir/main.c.o"

# External object files for target d
d_EXTERNAL_OBJECTS =

d : CMakeFiles/d.dir/main.c.o
d : CMakeFiles/d.dir/build.make
d : CMakeFiles/d.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/ddd/out/build/2/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking C executable d"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/d.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/d.dir/build: d

.PHONY : CMakeFiles/d.dir/build

CMakeFiles/d.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/d.dir/cmake_clean.cmake
.PHONY : CMakeFiles/d.dir/clean

CMakeFiles/d.dir/depend:
	cd /home/<USER>/ddd/out/build/2 && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/ddd /home/<USER>/ddd /home/<USER>/ddd/out/build/2 /home/<USER>/ddd/out/build/2 /home/<USER>/ddd/out/build/2/CMakeFiles/d.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/d.dir/depend

