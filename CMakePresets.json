{"version": 8, "configurePresets": [{"name": "2", "displayName": "GCC 10.2.1 arm-linux-gnueabihf", "description": "正在使用编译器: C = /usr/bin/arm-linux-gnueabihf-gcc, CXX = /usr/bin/arm-linux-gnueabihf-g++", "binaryDir": "${sourceDir}/out/build/${presetName}", "cacheVariables": {"CMAKE_INSTALL_PREFIX": "${sourceDir}/out/install/${presetName}", "CMAKE_C_COMPILER": "/usr/bin/arm-linux-gnueabihf-gcc", "CMAKE_CXX_COMPILER": "/usr/bin/arm-linux-gnueabihf-g++", "CMAKE_BUILD_TYPE": "Debug"}}]}